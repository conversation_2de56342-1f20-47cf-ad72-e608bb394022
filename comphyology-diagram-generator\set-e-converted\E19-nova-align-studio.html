<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIG E19: NovaAlign Studio Architecture</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .fig-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .description {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .patent-info {
            background: #f8f9fa;
            padding: 20px;
            border-left: 5px solid #007bff;
            margin: 20px;
        }
        
        .patent-info h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        
        .diagram-container {
            padding: 40px;
            text-align: center;
            background: #fafafa;
        }
        
        .mermaid {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 100%;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .inventor-info {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="fig-number">FIG E19</div>
            <div class="title">NovaAlign Studio Architecture (Mermaid)</div>
            <div class="description">
                AI alignment studio showing real-time monitoring and correction systems with consciousness-aware processing layers.
            </div>
        </div>
        
        <div class="patent-info">
            <h3>📋 Patent Information</h3>
            <p><strong>Claims:</strong> 29, 31 (AI Alignment)</p>
            <p><strong>Reference Numbers:</strong> 540-544</p>
            <p><strong>Innovation Focus:</strong> AI Safety and Real-time Alignment Monitoring</p>
            <p><strong>Source File:</strong> nova_align_studio.mmd</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%

graph TD
    subgraph "NovaAlign Studio Interaction Layers"
        A["User (Comphyological Architect)"] --> B("Design & Configuration Interface")
        B --> C{"NovaAlign Core Engine"}
        C -- System Blueprints --> D["Comphyological Pattern Library"]
        C -- Component Selection --> E["Nova Modules & CSEs Repository"]
        D & E --> F{"System Coherence Validation"}
        F -- Validated Design --> G["Deployment & Orchestration Manager"]
        G --> H["Operational NovaFuse Platform"]
        H -- Performance Feedback --> B
    end

    %% Styling for USPTO compliance (black and white)
    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
    classDef user fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:box3d
    classDef process fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:rectangle
    classDef decision fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:diamond
    classDef storage fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:cylinder
    classDef system fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:box3d,style=rounded
    
    class A user
    class B,G process
    class C,F decision
    class D,E storage
    class H system
            </div>
        </div>
        
        <div class="footer">
            <div class="inventor-info">
                <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies<br>
                Patent Title: Comphyology Universal Unified Field Theory Implementation System
            </div>
        </div>
    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
