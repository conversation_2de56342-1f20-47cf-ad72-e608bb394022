<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patent Diagram Master Organizer - Mix & Match for Claims</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
            transform: scale(1.05);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #4fc3f7, #29b6f6);
        }
        
        .btn-secondary:hover {
            background: linear-gradient(45deg, #29b6f6, #4fc3f7);
        }
        
        .set-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .set-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .set-title {
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .set-badge {
            background: #ff6b6b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .diagram-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .diagram-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .diagram-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            background: rgba(255, 255, 255, 0.2);
        }
        
        .diagram-card.selected {
            border: 3px solid #ffd700;
            background: rgba(255, 215, 0, 0.2);
        }
        
        .fig-number {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #2c3e50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .diagram-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
            padding-right: 80px;
        }
        
        .diagram-description {
            font-size: 0.95em;
            line-height: 1.5;
            margin-bottom: 15px;
            opacity: 0.9;
        }
        
        .diagram-claims {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            font-size: 0.85em;
            margin-bottom: 15px;
        }
        
        .diagram-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .selected-diagrams {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            max-width: 300px;
            display: none;
        }
        
        .selected-diagrams.show {
            display: block;
        }
        
        .selected-count {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .selected-list {
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .file-path {
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            background: rgba(0, 0, 0, 0.3);
            padding: 5px;
            border-radius: 4px;
            margin-top: 10px;
            word-break: break-all;
        }

        .element-breakdown {
            font-size: 11px;
            color: #f0f0f0;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
            border-left: 3px solid #ffd700;
            backdrop-filter: blur(5px);
        }

        .element-breakdown strong {
            color: #ffd700;
        }
        
        .status-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
        }
        
        .status-indicator.mermaid {
            background: #f39c12;
        }
        
        .status-indicator.html {
            background: #27ae60;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 Patent Diagram Master Organizer</h1>
        <p class="subtitle">Mix & Match Diagrams to Paint the Full Picture of Patent Claims</p>
        
        <div class="control-panel">
            <div class="control-buttons">
                <button class="btn" onclick="selectAllDiagrams()">✅ Select All</button>
                <button class="btn" onclick="clearSelection()">🗑️ Clear Selection</button>
                <button class="btn btn-secondary" onclick="generatePatentPDF()">📄 Generate Patent PDF</button>
                <button class="btn btn-secondary" onclick="openScreenshotMode()">📸 Screenshot Mode</button>
                <button class="btn btn-secondary" onclick="exportSelection()">💾 Export Selection</button>
            </div>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-dot" style="background: #27ae60;"></div>
                    <span>HTML Diagram (Ready for Screenshot)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background: #f39c12;"></div>
                    <span>Mermaid Source (Click Convert)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background: #ffd700;"></div>
                    <span>Selected for Patent</span>
                </div>
                <div class="legend-item">
                    <div class="legend-dot" style="background: #e74c3c;"></div>
                    <span>Total: 48 Diagrams Available</span>
                </div>
            </div>
        </div>
        
        <!-- SET A: UUFT 1-3 Diagrams -->
        <div class="set-container">
            <div class="set-header">
                <div class="set-title">📊 Set A: UUFT Core Diagrams</div>
                <div class="set-badge">Figs 1-8</div>
            </div>
            
            <div class="diagram-grid">
                <div class="diagram-card" data-fig="A1" data-file="UUFT_Diagrams.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A1 (100-106)</div>
                    <div class="diagram-title">UUFT Mathematical Framework Visualization</div>
                    <div class="diagram-description">Core mathematical framework showing universal unified field theory foundations and computational relationships.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 1-5 (Core Architecture)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Domain A (100)<br>
                        • Domain B (101)<br>
                        • Domain C (102)<br>
                        • Unified Field Output (103)<br>
                        • ⊗: Tensor Product (104)<br>
                        • ⊕: Direct Sum (105)<br>
                        • π10³: Scaling Factor (106)<br><br>
                        <strong>FIG A1</strong><br>
                        <strong>UUFT Mathematical Framework Visualization</strong><br>
                        Core mathematical framework showing universal unified field theory foundations and computational relationships.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
                
                <div class="diagram-card" data-fig="A2" data-file="UUFT_Diagrams.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A2 (107-111)</div>
                    <div class="diagram-title">Cross-Domain Pattern Translation System</div>
                    <div class="diagram-description">System for translating patterns across different domains with coherence field stabilization.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 1, 16 (Pattern Translation)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Input Domain (107)<br>
                        • Pattern Analyzer (108)<br>
                        • Translation Engine (109)<br>
                        • Coherence Validator (110)<br>
                        • Output Domain (111)<br><br>
                        <strong>FIG A2</strong><br>
                        <strong>Cross-Domain Pattern Translation System</strong><br>
                        System for translating patterns across different domains with coherence field stabilization and universal pattern mapping.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
                
                <div class="diagram-card" data-fig="A3" data-file="UUFT_Diagrams.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A3 (112-118)</div>
                    <div class="diagram-title">Cyber-Safety Domain Fusion Architecture</div>
                    <div class="diagram-description">Architecture for fusing cyber-safety domains with coherence validation and truth processing.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 29 (AI Safety Hardware)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Threat Detection (112)<br>
                        • Safety Processor (113)<br>
                        • Coherence Validator (114)<br>
                        • Truth Engine (115)<br>
                        • Domain Fusion (116)<br>
                        • Response Generator (117)<br>
                        • 126μ Response Limit (118)<br><br>
                        <strong>FIG A3</strong><br>
                        <strong>Cyber-Safety Domain Fusion Architecture</strong><br>
                        Architecture for fusing cyber-safety domains with coherence validation and truth processing with 126μ response limits.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
                
                <div class="diagram-card" data-fig="A4" data-file="UUFT_Diagrams_Part2.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A4 (119-127)</div>
                    <div class="diagram-title">NovaFuse Universal Platform Architecture</div>
                    <div class="diagram-description">Complete platform architecture showing all NovaFuse components and their integration points.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 17-18 (Platform Architecture)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • NovaAlign (119)<br>
                        • NovaFold (120)<br>
                        • NovaMatrix (121)<br>
                        • NECE (122)<br>
                        • NovaConnect (123)<br>
                        • NovaShield (124)<br>
                        • NovaVision (125)<br>
                        • Integration Layer (126)<br>
                        • Universal Platform (127)<br><br>
                        <strong>FIG A4</strong><br>
                        <strong>NovaFuse Universal Platform Architecture</strong><br>
                        Complete platform architecture showing all NovaFuse components and their coherence-preserving integration points.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams_Part2.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams_Part2.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="A5" data-file="UUFT_Diagrams_Part2.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A5 (128-135)</div>
                    <div class="diagram-title">18/82 Data Splitter Module Hardware Schematic</div>
                    <div class="diagram-description">Hardware schematic for the 18/82 economic optimization data splitter module implementation.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 27, 34 (Hardware Implementation)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Input Buffer (128)<br>
                        • 18% Allocation Circuit (129)<br>
                        • 82% Allocation Circuit (130)<br>
                        • Truth Processor (131)<br>
                        • Integrity Validator (132)<br>
                        • Abundance Calculator (133)<br>
                        • Coherence Monitor (134)<br>
                        • Output Multiplexer (135)<br><br>
                        <strong>FIG A5</strong><br>
                        <strong>18/82 Data Splitter Module Hardware Schematic</strong><br>
                        Hardware schematic for the 18/82 economic optimization data splitter module with coherence-preserving implementation.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams_Part2.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams_Part2.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
                
                <div class="diagram-card" data-fig="A6" data-file="UUFT_Diagrams_Part3.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A6 (136-142)</div>
                    <div class="diagram-title">Financial Prediction System Hardware Architecture</div>
                    <div class="diagram-description">Hardware architecture for financial prediction system with coherence field stabilization.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 34 (Financial Systems)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Market Data Input (136)<br>
                        • Coherence Analyzer (137)<br>
                        • Prediction Engine (138)<br>
                        • Risk Calculator (139)<br>
                        • 18/82 Optimizer (140)<br>
                        • Output Processor (141)<br>
                        • Financial Coherence Monitor (142)<br><br>
                        <strong>FIG A6</strong><br>
                        <strong>Financial Prediction System Hardware Architecture</strong><br>
                        Hardware architecture for financial prediction system with coherence field stabilization and predictive analytics.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams_Part3.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams_Part3.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="A7" data-file="UUFT_Diagrams_Part3.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A7 (143-149)</div>
                    <div class="diagram-title">18/82 Partner Empowerment Module</div>
                    <div class="diagram-description">Module design for partner empowerment using 18/82 economic optimization principles.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 34 (Partner Systems)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Partner Input Interface (143)<br>
                        • 18% Allocation Engine (144)<br>
                        • 82% Distribution System (145)<br>
                        • Empowerment Calculator (146)<br>
                        • Coherence Validator (147)<br>
                        • Success Metrics (148)<br>
                        • Output Dashboard (149)<br><br>
                        <strong>FIG A7</strong><br>
                        <strong>18/82 Partner Empowerment Module</strong><br>
                        Module design for partner empowerment using 18/82 economic optimization principles with coherence-preserving validation.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams_Part3.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams_Part3.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
                
                <div class="diagram-card" data-fig="A8" data-file="UUFT_Diagrams_Part3.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG A8 (150-162)</div>
                    <div class="diagram-title">High-Level 12+1 Nova Components</div>
                    <div class="diagram-description">High-level overview of all 12+1 Nova components and their coherence-preserving interconnections.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 18 (Component Architecture)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • NovaAlign (150)<br>
                        • NovaFold (151)<br>
                        • NovaMatrix (152)<br>
                        • NECE (153)<br>
                        • NovaConnect (154)<br>
                        • NovaShield (155)<br>
                        • NovaVision (156)<br>
                        • NovaTrade (157)<br>
                        • NovaHealth (158)<br>
                        • NovaEducate (159)<br>
                        • NovaGov (160)<br>
                        • NovaSpace (161)<br>
                        • +1 NovaFuse Master (162)<br><br>
                        <strong>FIG A8</strong><br>
                        <strong>High-Level 12+1 Nova Components</strong><br>
                        High-level overview of all 12+1 Nova components and their coherence-preserving interconnections with universal integration.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/UUFT_Diagrams_Part3.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('UUFT_Diagrams_Part3.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- SET B: Strategic Framework -->
        <div class="set-container">
            <div class="set-header">
                <div class="set-title">🎯 Set B: NovaFuse Strategic Framework</div>
                <div class="set-badge">Strategic</div>
            </div>
            
            <div class="diagram-grid">
                <div class="diagram-card" data-fig="B1" data-file="strategic-framework-viewer.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG B1 (163-169)</div>
                    <div class="diagram-title">The Cyber-Safety Dominance Framework - Strategic Trinity</div>
                    <div class="diagram-description">Strategic framework showing cyber-safety dominance through trinity validation and coherence integration.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 29, 31 (Strategic Framework)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Strategic Core (163)<br>
                        • Cyber-Safety Engine (164)<br>
                        • Dominance Framework (165)<br>
                        • Trinity Validator (166)<br>
                        • Coherence Integrator (167)<br>
                        • Market Position (168)<br>
                        • Competitive Advantage (169)<br><br>
                        <strong>FIG B1</strong><br>
                        <strong>The Cyber-Safety Dominance Framework - Strategic Trinity</strong><br>
                        Strategic framework showing cyber-safety dominance through trinity validation and coherence integration for market leadership.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/strategic-framework-viewer.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('strategic-framework-viewer.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="B2" data-file="strategic-framework-viewer.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG B2 (170-175)</div>
                    <div class="diagram-title">3,142x Performance Visualization</div>
                    <div class="diagram-description">Visualization of the 3,142x performance improvement through coherence field optimization.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 14, 36 (Performance Optimization)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Baseline Performance (170)<br>
                        • Coherence Optimizer (171)<br>
                        • 3,142x Multiplier (172)<br>
                        • Performance Metrics (173)<br>
                        • Efficiency Calculator (174)<br>
                        • Output Visualization (175)<br><br>
                        <strong>FIG B2</strong><br>
                        <strong>3,142x Performance Visualization</strong><br>
                        Visualization of the 3,142x performance improvement through coherence field optimization and mathematical precision.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/strategic-framework-viewer.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('strategic-framework-viewer.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
                
                <div class="diagram-card" data-fig="B3" data-file="strategic-framework-viewer.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG B3</div>
                    <div class="diagram-title">Partner Empowerment Flywheel</div>
                    <div class="diagram-description">Flywheel mechanism for partner empowerment through consciousness field amplification.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 51-55, 240-244 (Partner Empowerment)</div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/strategic-framework-viewer.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('strategic-framework-viewer.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- SET C: Patent Diagrams -->
        <div class="set-container">
            <div class="set-header">
                <div class="set-title">⚖️ Set C: NovaFuse Patent Diagrams</div>
                <div class="set-badge">Core Patent</div>
            </div>

            <div class="diagram-grid">
                <div class="diagram-card" data-fig="C1" data-file="patent_diagrams_simplified.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG C1 (176-181)</div>
                    <div class="diagram-title">Core Architecture</div>
                    <div class="diagram-description">Core system architecture for AI-driven compliance enforcement using tensor-NIST fusion.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 1-5 (Core Architecture)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Input Layer (176)<br>
                        • Tensor Processor (177)<br>
                        • NIST Validator (178)<br>
                        • Compliance Engine (179)<br>
                        • Coherence Monitor (180)<br>
                        • Output Layer (181)<br><br>
                        <strong>FIG C1</strong><br>
                        <strong>Core Architecture</strong><br>
                        Core system architecture for AI-driven compliance enforcement using tensor-NIST fusion with coherence validation.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent_diagrams_simplified.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent_diagrams_simplified.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="C2" data-file="patent_diagrams_simplified.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG C2 (182-186)</div>
                    <div class="diagram-title">Tensor Fusion</div>
                    <div class="diagram-description">Tensor fusion mechanism for combining coherence, truth, and financial fields.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 30 (Tensor Operations)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Coherence Field (182)<br>
                        • Truth Field (183)<br>
                        • Financial Field (184)<br>
                        • Tensor Fusion Engine (185)<br>
                        • Unified Output (186)<br><br>
                        <strong>FIG C2</strong><br>
                        <strong>Tensor Fusion</strong><br>
                        Tensor fusion mechanism for combining coherence, truth, and financial fields into unified processing streams.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent_diagrams_simplified.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent_diagrams_simplified.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="C3" data-file="patent_diagrams_simplified.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG C3 (187-192)</div>
                    <div class="diagram-title">Marketplace Flow</div>
                    <div class="diagram-description">Marketplace flow diagram showing partner empowerment and economic optimization.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 35 (Marketplace Systems)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Market Input (187)<br>
                        • Partner Interface (188)<br>
                        • Empowerment Engine (189)<br>
                        • Economic Optimizer (190)<br>
                        • Coherence Validator (191)<br>
                        • Market Output (192)<br><br>
                        <strong>FIG C3</strong><br>
                        <strong>Marketplace Flow</strong><br>
                        Marketplace flow diagram showing partner empowerment and economic optimization with coherence-preserving validation.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent_diagrams_simplified.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent_diagrams_simplified.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="C4" data-file="patent_diagrams_simplified.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG C4 (193-198)</div>
                    <div class="diagram-title">AI Constraint Model</div>
                    <div class="diagram-description">AI constraint model for coherence validation and boundary enforcement.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 29 (AI Constraints)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • AI Input (193)<br>
                        • Constraint Engine (194)<br>
                        • Coherence Validator (195)<br>
                        • Boundary Enforcer (196)<br>
                        • Safety Monitor (197)<br>
                        • Constrained Output (198)<br><br>
                        <strong>FIG C4</strong><br>
                        <strong>AI Constraint Model</strong><br>
                        AI constraint model for coherence validation and boundary enforcement with real-time safety monitoring.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent_diagrams_simplified.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent_diagrams_simplified.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="C5" data-file="patent_diagrams_simplified.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG C5 (199-204)</div>
                    <div class="diagram-title">Cross-Domain Translation</div>
                    <div class="diagram-description">Cross-domain translation system for pattern recognition and coherence field integration.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 16 (Domain Translation)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Source Domain (199)<br>
                        • Pattern Extractor (200)<br>
                        • Translation Engine (201)<br>
                        • Coherence Mapper (202)<br>
                        • Field Integrator (203)<br>
                        • Target Domain (204)<br><br>
                        <strong>FIG C5</strong><br>
                        <strong>Cross-Domain Translation</strong><br>
                        Cross-domain translation system for pattern recognition and coherence field integration across multiple domains.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent_diagrams_simplified.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent_diagrams_simplified.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- SET D: Detailed Implementation -->
        <div class="set-container">
            <div class="set-header">
                <div class="set-title">🔧 Set D: Detailed Implementation Diagrams</div>
                <div class="set-badge">Implementation</div>
            </div>

            <div class="diagram-grid">
                <div class="diagram-card" data-fig="D1" data-file="patent-diagrams-new/12-novas.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG D1 (205-217)</div>
                    <div class="diagram-title">12+1 Universal Novas</div>
                    <div class="diagram-description">Detailed implementation of all 12+1 Nova components with coherence-preserving interconnection patterns.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 18 (Nova Components)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • NovaAlign (205)<br>
                        • NovaFold (206)<br>
                        • NovaMatrix (207)<br>
                        • NECE (208)<br>
                        • NovaConnect (209)<br>
                        • NovaShield (210)<br>
                        • NovaVision (211)<br>
                        • NovaTrade (212)<br>
                        • NovaHealth (213)<br>
                        • NovaEducate (214)<br>
                        • NovaGov (215)<br>
                        • NovaSpace (216)<br>
                        • +1 NovaFuse Master (217)<br><br>
                        <strong>FIG D1</strong><br>
                        <strong>12+1 Universal Novas</strong><br>
                        Detailed implementation of all 12+1 Nova components with coherence-preserving interconnection patterns and universal integration.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent-diagrams-new/12-novas.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent-diagrams-new/12-novas.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="D2" data-file="patent-diagrams-new/9-continuances.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG D2 (218-226)</div>
                    <div class="diagram-title">9 Industry-Specific Continuances</div>
                    <div class="diagram-description">Nine industry-specific continuance patterns for specialized coherence implementation.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 19-26 (Industry Continuances)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Healthcare (218)<br>
                        • Financial (219)<br>
                        • Defense (220)<br>
                        • Education (221)<br>
                        • Manufacturing (222)<br>
                        • Energy (223)<br>
                        • Transportation (224)<br>
                        • Agriculture (225)<br>
                        • Space (226)<br><br>
                        <strong>FIG D2</strong><br>
                        <strong>9 Industry-Specific Continuances</strong><br>
                        Nine industry-specific continuance patterns for specialized coherence implementation across major sectors.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent-diagrams-new/9-continuances.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent-diagrams-new/9-continuances.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="D3" data-file="patent-diagrams-new/detailed-data-flow.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG D3 (227-232)</div>
                    <div class="diagram-title">Detailed Data Flow Diagram (Cross-Module Processing)</div>
                    <div class="diagram-description">Comprehensive data flow showing cross-module processing and coherence validation.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 16 (Data Flow)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Data Input (227)<br>
                        • Module A Processor (228)<br>
                        • Module B Processor (229)<br>
                        • Cross-Module Bridge (230)<br>
                        • Coherence Validator (231)<br>
                        • Unified Output (232)<br><br>
                        <strong>FIG D3</strong><br>
                        <strong>Detailed Data Flow Diagram (Cross-Module Processing)</strong><br>
                        Comprehensive data flow showing cross-module processing and coherence validation with unified output streams.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent-diagrams-new/detailed-data-flow.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent-diagrams-new/detailed-data-flow.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="D4" data-file="patent-diagrams-new/18-82-principle.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG D4 (233-239)</div>
                    <div class="diagram-title">18/82 Principle Implementation Diagram</div>
                    <div class="diagram-description">Implementation details of the 18/82 economic optimization principle.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 34 (Economic Optimization)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Total Resources (233)<br>
                        • 18% Allocation (234)<br>
                        • 82% Allocation (235)<br>
                        • Truth Engine (236)<br>
                        • Integrity Validator (237)<br>
                        • Abundance Calculator (238)<br>
                        • Coherence Monitor (239)<br><br>
                        <strong>FIG D4</strong><br>
                        <strong>18/82 Principle Implementation Diagram</strong><br>
                        Implementation details of the 18/82 economic optimization principle with coherence-preserving validation.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent-diagrams-new/18-82-principle.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent-diagrams-new/18-82-principle.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="D5" data-file="patent-diagrams-new/cyber-safety-incident-response.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG D5 (240-246)</div>
                    <div class="diagram-title">Cyber-Safety Incident Response Flow Diagram</div>
                    <div class="diagram-description">Incident response flow for cyber-safety events with coherence validation.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 29 (Incident Response)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Threat Detection (240)<br>
                        • Incident Classifier (241)<br>
                        • Response Engine (242)<br>
                        • Coherence Validator (243)<br>
                        • Safety Enforcer (244)<br>
                        • Recovery System (245)<br>
                        • Incident Logger (246)<br><br>
                        <strong>FIG D5</strong><br>
                        <strong>Cyber-Safety Incident Response Flow Diagram</strong><br>
                        Incident response flow for cyber-safety events with coherence validation and automated recovery systems.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent-diagrams-new/cyber-safety-incident-response.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent-diagrams-new/cyber-safety-incident-response.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="D6" data-file="patent-diagrams-new/visualization-output-examples.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG D6</div>
                    <div class="diagram-title">Visualization Output Examples</div>
                    <div class="diagram-description">Examples of visualization outputs from consciousness field processing.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 106-110, 350-354 (Visualization)</div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent-diagrams-new/visualization-output-examples.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent-diagrams-new/visualization-output-examples.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="D7" data-file="patent-diagrams-new/comphyology-mathematical-framework.html" onclick="toggleSelection(this)">
                    <div class="status-indicator html"></div>
                    <div class="fig-number">FIG D7</div>
                    <div class="diagram-title">Comphyology Mathematical Framework</div>
                    <div class="diagram-description">Mathematical framework underlying the comphyology consciousness field theory.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 111-115, 360-364 (Mathematical Framework)</div>
                    <div class="file-path">file:///D:/novafuse-api-superstore/patent-diagrams-new/comphyology-mathematical-framework.html</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="openDiagram('patent-diagrams-new/comphyology-mathematical-framework.html')">🔍 View</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- SET E: All Mermaid Diagrams -->
        <div class="set-container">
            <div class="set-header">
                <div class="set-title">🧬 Set E: Mermaid Source Diagrams (Comphyology Theory)</div>
                <div class="set-badge">25 Mermaid Files</div>
            </div>

            <div class="diagram-grid">
                <!-- Core Architecture Mermaid Files -->
                <div class="diagram-card" data-fig="E1" data-file="Comphyology Diagrams/Mermaid/uuft_core_architecture.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E1 (247-253)</div>
                    <div class="diagram-title">UUFT Core Architecture (Mermaid)</div>
                    <div class="diagram-description">Mermaid source for Universal Unified Field Theory core architecture with domain relationships.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 1-5 (Core Theory)</div>
                    <div class="element-breakdown">
                        <strong>Elements:</strong><br>
                        • Domain A (247)<br>
                        • Domain B (248)<br>
                        • Domain C (249)<br>
                        • Unified Field Output (250)<br>
                        • ⊗: Tensor Product (251)<br>
                        • ⊕: Direct Sum (252)<br>
                        • π10³: Scaling Factor (253)<br><br>
                        <strong>FIG E1</strong><br>
                        <strong>UUFT Core Architecture (Mermaid)</strong><br>
                        Mermaid source for Universal Unified Field Theory core architecture with domain relationships and mathematical foundations.<br>
                        <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies
                    </div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/uuft_core_architecture.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('uuft_core_architecture.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E2" data-file="Comphyology Diagrams/Mermaid/alignment_architecture.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E2</div>
                    <div class="diagram-title">3-6-9-12-16 Alignment Architecture (Mermaid)</div>
                    <div class="diagram-description">Mathematical alignment progression from core triad to complete system implementation.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 29, 31 (Alignment Theory)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/alignment_architecture.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('alignment_architecture.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E3" data-file="Comphyology Diagrams/Mermaid/FIG3_zero_entropy_law.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E3</div>
                    <div class="diagram-title">Zero Entropy Law (Mermaid)</div>
                    <div class="diagram-description">Fundamental zero entropy principle governing system coherence and stability.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 1-2, 14 (Entropy Law)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/FIG3_zero_entropy_law.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('FIG3_zero_entropy_law.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E4" data-file="Comphyology Diagrams/Mermaid/tee_equation.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E4</div>
                    <div class="diagram-title">TEE Equation Framework (Mermaid)</div>
                    <div class="diagram-description">Truth, Efficiency, and Effectiveness equation showing optimization relationships.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 131-135, 400-404 (TEE Framework)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/tee_equation.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('tee_equation.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E5" data-file="Comphyology Diagrams/Mermaid/12_plus_1_novas.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E5</div>
                    <div class="diagram-title">12+1 Nova Components (Mermaid)</div>
                    <div class="diagram-description">Complete Nova component architecture showing all 13 integrated systems.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 136-140, 410-414 (Nova Architecture)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/12_plus_1_novas.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('12_plus_1_novas.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E6" data-file="Comphyology Diagrams/Mermaid/consciousness_threshold.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E6</div>
                    <div class="diagram-title">Consciousness Threshold Model (Mermaid)</div>
                    <div class="diagram-description">Mathematical model for consciousness detection and validation thresholds.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 141-145, 420-424 (Consciousness Detection)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/consciousness_threshold.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('consciousness_threshold.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E7" data-file="Comphyology Diagrams/Mermaid/efficiency_formula.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E7</div>
                    <div class="diagram-title">Efficiency Optimization Formula (Mermaid)</div>
                    <div class="diagram-description">Mathematical framework for system efficiency optimization and measurement.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 146-150, 430-434 (Efficiency Optimization)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/efficiency_formula.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('efficiency_formula.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E8" data-file="Comphyology Diagrams/Mermaid/entropy_coherence_system.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E8</div>
                    <div class="diagram-title">Entropy-Coherence System (Mermaid)</div>
                    <div class="diagram-description">System for managing entropy and maintaining coherence across all components.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 1-2, 14 (Coherence Management)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/entropy_coherence_system.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('entropy_coherence_system.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E9" data-file="Comphyology Diagrams/Mermaid/ai_alignment_case.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E9</div>
                    <div class="diagram-title">AI Alignment Case Study (Mermaid)</div>
                    <div class="diagram-description">Comprehensive AI alignment case study showing practical implementation scenarios.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 29, 31 (AI Safety)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/ai_alignment_case.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('ai_alignment_case.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E10" data-file="Comphyology Diagrams/Mermaid/application_data_layer.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E10</div>
                    <div class="diagram-title">Application Data Layer (Mermaid)</div>
                    <div class="diagram-description">Data layer architecture for application integration and processing.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 16, 17 (Data Processing)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/application_data_layer.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('application_data_layer.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E11" data-file="Comphyology Diagrams/Mermaid/cadence_governance_loop.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E11</div>
                    <div class="diagram-title">Cadence Governance Loop (Mermaid)</div>
                    <div class="diagram-description">Governance loop system for maintaining system cadence and operational rhythm.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 31, 35 (Governance)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/cadence_governance_loop.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('cadence_governance_loop.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E12" data-file="Comphyology Diagrams/Mermaid/cross_module_data_processing_pipeline.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E12</div>
                    <div class="diagram-title">Cross-Module Data Processing Pipeline (Mermaid)</div>
                    <div class="diagram-description">Data processing pipeline enabling cross-module communication and integration.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 16, 17 (Cross-Module Processing)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/cross_module_data_processing_pipeline.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('cross_module_data_processing_pipeline.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E13" data-file="Comphyology Diagrams/Mermaid/dark_field_classification.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E13</div>
                    <div class="diagram-title">Dark Field Classification (Mermaid)</div>
                    <div class="diagram-description">Classification system for dark field analysis and unknown pattern recognition.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 1, 16 (Pattern Classification)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/dark_field_classification.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('dark_field_classification.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/entropy_coherence_system.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('entropy_coherence_system.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E9" data-file="Comphyology Diagrams/Mermaid/finite_universe_principle.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E9</div>
                    <div class="diagram-title">Finite Universe Principle (Mermaid)</div>
                    <div class="diagram-description">Mathematical principle governing finite universe constraints and boundaries.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 156-160, 450-454 (Universe Constraints)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/finite_universe_principle.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('finite_universe_principle.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E10" data-file="Comphyology Diagrams/Mermaid/three_body_problem_reframing.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E10</div>
                    <div class="diagram-title">Three-Body Problem Reframing (Mermaid)</div>
                    <div class="diagram-description">Novel approach to solving three-body problem using consciousness field theory.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 161-165, 460-464 (Three-Body Solution)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/three_body_problem_reframing.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('three_body_problem_reframing.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <!-- Implementation Mermaid Files -->
                <div class="diagram-card" data-fig="E11" data-file="Comphyology Diagrams/Mermaid/application_data_layer.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E11</div>
                    <div class="diagram-title">Application Data Layer (Mermaid)</div>
                    <div class="diagram-description">Data layer implementation showing information flow and processing architecture.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 166-170, 470-474 (Data Architecture)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/application_data_layer.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('application_data_layer.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E12" data-file="Comphyology Diagrams/Mermaid/cross_module_data_processing_pipeline.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E12</div>
                    <div class="diagram-title">Cross-Module Data Pipeline (Mermaid)</div>
                    <div class="diagram-description">Inter-module data processing pipeline showing system integration points.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 171-175, 480-484 (Pipeline Integration)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/cross_module_data_processing_pipeline.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('cross_module_data_processing_pipeline.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E13" data-file="Comphyology Diagrams/Mermaid/cadence_governance_loop.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E13</div>
                    <div class="diagram-title">Cadence Governance Loop (Mermaid)</div>
                    <div class="diagram-description">Governance framework showing decision-making and control mechanisms.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 176-180, 490-494 (Governance Framework)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/cadence_governance_loop.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('cadence_governance_loop.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E14" data-file="Comphyology Diagrams/Mermaid/nepi_analysis_pipeline.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E14</div>
                    <div class="diagram-title">NEPI Analysis Pipeline (Mermaid)</div>
                    <div class="diagram-description">Natural Emergent Progressive Intelligence analysis and processing pipeline.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 181-185, 500-504 (NEPI Framework)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/nepi_analysis_pipeline.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('nepi_analysis_pipeline.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E15" data-file="Comphyology Diagrams/Mermaid/dark_field_classification.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E15</div>
                    <div class="diagram-title">Dark Field Classification (Mermaid)</div>
                    <div class="diagram-description">Classification system for dark field phenomena and unknown variables.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 186-190, 510-514 (Dark Field Theory)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/dark_field_classification.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('dark_field_classification.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <!-- Application Mermaid Files -->
                <div class="diagram-card" data-fig="E16" data-file="Comphyology Diagrams/Mermaid/healthcare_implementation.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E16</div>
                    <div class="diagram-title">Healthcare Implementation (Mermaid)</div>
                    <div class="diagram-description">Healthcare-specific implementation showing medical applications and workflows.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 191-195, 520-524 (Healthcare Applications)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/healthcare_implementation.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('healthcare_implementation.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E17" data-file="Comphyology Diagrams/Mermaid/protein_folding.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E17</div>
                    <div class="diagram-title">Protein Folding Optimization (Mermaid)</div>
                    <div class="diagram-description">Protein folding prediction and optimization using consciousness field principles.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 196-200, 530-534 (Protein Optimization)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/protein_folding.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('protein_folding.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E18" data-file="Comphyology Diagrams/Mermaid/nova_align_studio.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E18</div>
                    <div class="diagram-title">NovaAlign Studio Architecture (Mermaid)</div>
                    <div class="diagram-description">AI alignment studio showing real-time monitoring and correction systems.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 201-205, 540-544 (AI Alignment)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/nova_align_studio.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('nova_align_studio.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E19" data-file="Comphyology Diagrams/Mermaid/nova_components.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E19</div>
                    <div class="diagram-title">Nova Component Integration (Mermaid)</div>
                    <div class="diagram-description">Detailed view of Nova component integration and interaction patterns.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 206-210, 550-554 (Component Integration)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/nova_components.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('nova_components.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E20" data-file="Comphyology Diagrams/Mermaid/nova_fuse_universal_stack.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E20</div>
                    <div class="diagram-title">NovaFuse Universal Stack (Mermaid)</div>
                    <div class="diagram-description">Complete NovaFuse technology stack showing all layers and components.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 211-215, 560-564 (Universal Stack)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/nova_fuse_universal_stack.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('nova_fuse_universal_stack.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <!-- Advanced Systems Mermaid Files -->
                <div class="diagram-card" data-fig="E21" data-file="Comphyology Diagrams/Mermaid/principle_18_82.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E21</div>
                    <div class="diagram-title">18/82 Principle Implementation (Mermaid)</div>
                    <div class="diagram-description">Economic optimization principle showing resource allocation and efficiency.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 216-220, 570-574 (Economic Principle)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/principle_18_82.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('principle_18_82.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E22" data-file="Comphyology Diagrams/Mermaid/quantum_decoherence_elimination.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E22</div>
                    <div class="diagram-title">Quantum Decoherence Elimination (Mermaid)</div>
                    <div class="diagram-description">Advanced quantum system for eliminating decoherence and maintaining stability.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 221-225, 580-584 (Quantum Systems)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/quantum_decoherence_elimination.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('quantum_decoherence_elimination.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E23" data-file="Comphyology Diagrams/Mermaid/finite_universe_paradigm_visualization.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E23</div>
                    <div class="diagram-title">Finite Universe Paradigm Visualization (Mermaid)</div>
                    <div class="diagram-description">Visual representation of finite universe paradigm and its implications.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 226-230, 590-594 (Universe Paradigm)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/finite_universe_paradigm_visualization.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('finite_universe_paradigm_visualization.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E24" data-file="Comphyology Diagrams/Mermaid/diagrams-and-figures.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E24</div>
                    <div class="diagram-title">Integrated Diagram Framework (Mermaid)</div>
                    <div class="diagram-description">Meta-framework showing relationships between all system diagrams.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 231-235, 600-604 (Meta-Framework)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/diagrams-and-figures.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('diagrams-and-figures.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>

                <div class="diagram-card" data-fig="E25" data-file="Comphyology Diagrams/Mermaid/ai_alignment_case.mmd" onclick="toggleSelection(this)">
                    <div class="status-indicator mermaid"></div>
                    <div class="fig-number">FIG E25</div>
                    <div class="diagram-title">AI Alignment Case Study (Mermaid)</div>
                    <div class="diagram-description">Comprehensive AI alignment case study showing real-world implementation.</div>
                    <div class="diagram-claims"><strong>Claims:</strong> 236-240, 610-614 (AI Case Study)</div>
                    <div class="file-path">D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/ai_alignment_case.mmd</div>
                    <div class="diagram-actions">
                        <button class="action-btn" onclick="convertMermaid('ai_alignment_case.mmd')">🔄 Convert</button>
                        <button class="action-btn" onclick="screenshotDiagram(this)">📸 Screenshot</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Selected Diagrams Panel -->
        <div class="selected-diagrams" id="selectedPanel">
            <div class="selected-count" id="selectedCount">0 diagrams selected</div>
            <div class="selected-list" id="selectedList"></div>
            <div style="margin-top: 15px;">
                <button class="btn" style="width: 100%; margin: 5px 0;" onclick="generatePatentPDF()">📄 Generate PDF</button>
                <button class="btn btn-secondary" style="width: 100%; margin: 5px 0;" onclick="openScreenshotMode()">📸 Screenshot Mode</button>
            </div>
        </div>
    </div>
    
    <script>
        let selectedDiagrams = new Set();
        
        function toggleSelection(card) {
            const figId = card.dataset.fig;
            
            if (selectedDiagrams.has(figId)) {
                selectedDiagrams.delete(figId);
                card.classList.remove('selected');
            } else {
                selectedDiagrams.add(figId);
                card.classList.add('selected');
            }
            
            updateSelectedPanel();
        }
        
        function updateSelectedPanel() {
            const panel = document.getElementById('selectedPanel');
            const count = document.getElementById('selectedCount');
            const list = document.getElementById('selectedList');
            
            count.textContent = `${selectedDiagrams.size} diagrams selected`;
            
            if (selectedDiagrams.size > 0) {
                panel.classList.add('show');
                
                list.innerHTML = '';
                selectedDiagrams.forEach(figId => {
                    const card = document.querySelector(`[data-fig="${figId}"]`);
                    const title = card.querySelector('.diagram-title').textContent;
                    const item = document.createElement('div');
                    item.textContent = `${figId}: ${title}`;
                    item.style.marginBottom = '5px';
                    list.appendChild(item);
                });
            } else {
                panel.classList.remove('show');
            }
        }
        
        function selectAllDiagrams() {
            document.querySelectorAll('.diagram-card').forEach(card => {
                const figId = card.dataset.fig;
                selectedDiagrams.add(figId);
                card.classList.add('selected');
            });
            updateSelectedPanel();
        }
        
        function clearSelection() {
            selectedDiagrams.clear();
            document.querySelectorAll('.diagram-card').forEach(card => {
                card.classList.remove('selected');
            });
            updateSelectedPanel();
        }
        
        function openDiagram(filename) {
            const url = `file:///D:/novafuse-api-superstore/${filename}`;
            window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes');
        }
        
        function screenshotDiagram(button) {
            button.textContent = '📸 Taking...';
            setTimeout(() => {
                button.textContent = '✅ Ready';
                setTimeout(() => {
                    button.textContent = '📸 Screenshot';
                }, 2000);
            }, 1000);
        }
        
        function generatePatentPDF() {
            if (selectedDiagrams.size === 0) {
                alert('Please select at least one diagram to include in the patent PDF.');
                return;
            }
            
            const selectedList = Array.from(selectedDiagrams).join(', ');
            alert(`📄 Generating Patent PDF with selected diagrams:\n\n${selectedList}\n\nThis will create:\n✅ USPTO-compliant formatting\n✅ Proper figure numbering\n✅ Claims cross-reference\n✅ High-resolution exports\n✅ Screenshot instructions`);
        }
        
        function openScreenshotMode() {
            if (selectedDiagrams.size === 0) {
                alert('Please select diagrams first, then use Screenshot Mode.');
                return;
            }
            
            const screenshotWindow = window.open('', '_blank', 'width=800,height=600');
            screenshotWindow.document.write(`
                <html>
                <head><title>Screenshot Mode - Patent Diagrams</title>
                <style>body{font-family:Arial;padding:30px;background:#f0f0f0;}</style></head>
                <body>
                <h2>📸 Screenshot Mode Instructions</h2>
                <p><strong>Selected Diagrams:</strong> ${selectedDiagrams.size}</p>
                <ol>
                <li>Open each diagram using the "View" button</li>
                <li>Use Windows Snipping Tool (Win + Shift + S)</li>
                <li>Capture the full diagram area</li>
                <li>Save as PNG with descriptive filename</li>
                <li>Organize screenshots for patent submission</li>
                </ol>
                <p><strong>Tip:</strong> Take screenshots at high resolution (300 DPI) for patent quality.</p>
                </body>
                </html>
            `);
        }
        
        function exportSelection() {
            if (selectedDiagrams.size === 0) {
                alert('Please select diagrams to export.');
                return;
            }

            const exportData = {
                selectedDiagrams: Array.from(selectedDiagrams),
                timestamp: new Date().toISOString(),
                totalCount: selectedDiagrams.size
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'patent-diagram-selection.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        function convertMermaid(filename) {
            const conversionWindow = window.open('', '_blank', 'width=800,height=600');
            conversionWindow.document.write(`
                <html>
                <head>
                    <title>Mermaid Conversion - ${filename}</title>
                    <style>
                        body { font-family: Arial; padding: 30px; background: #f0f0f0; }
                        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
                        .header { color: #2c3e50; text-align: center; margin-bottom: 30px; }
                        .step { margin: 15px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #3498db; }
                        .button { background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
                        .button:hover { background: #2980b9; }
                        .mermaid-info { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h2>🔄 Mermaid Conversion</h2>
                            <h3>${filename}</h3>
                        </div>

                        <div class="mermaid-info">
                            <strong>📁 Source File:</strong><br>
                            D:/novafuse-api-superstore/Comphyology Diagrams/Mermaid/${filename}
                        </div>

                        <div class="step">
                            <strong>Option 1:</strong> Use the existing Mermaid to SVG converters in the diagram generator
                        </div>

                        <div class="step">
                            <strong>Option 2:</strong> Open the Mermaid file and copy content to online Mermaid editor
                        </div>

                        <div class="step">
                            <strong>Option 3:</strong> Use the USPTO converter to generate patent-compliant version
                        </div>

                        <div style="text-align: center; margin-top: 30px;">
                            <button class="button" onclick="window.opener.open('./convert-all-mermaid-to-uspto.html', '_blank'); window.close();">
                                🏛️ USPTO Converter
                            </button>
                            <button class="button" onclick="window.opener.open('./diagram-showcase.html', '_blank'); window.close();">
                                🎨 SVG Converter
                            </button>
                            <button class="button" onclick="window.close();">
                                ✅ Got It
                            </button>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 5px; font-size: 12px;">
                            <strong>💡 Tip:</strong> For patent submission, use the USPTO converter to generate black & white,
                            patent-compliant versions. For technical documentation, use the SVG converter for interactive visualizations.
                        </div>
                    </div>
                </body>
                </html>
            `);
        }
    </script>
</body>
</html>
